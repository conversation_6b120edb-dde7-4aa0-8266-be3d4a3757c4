<?php
/**
 * OPcache Status Monitor for PHP 8.3 on AWS
 * This script helps monitor PHP 8.3 performance optimizations
 */

// Security check - only allow in development or with proper authentication
if (!isset($_SESSION)) {
    session_start();
}

// Simple security - you can enhance this
$allowed = true; // Set to false in production and add proper authentication

if (!$allowed) {
    die("Access denied. This page is for development/monitoring only.");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP 8.3 OPcache Status - AWS Performance Monitor</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; margin: 5px 0; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; min-width: 150px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .warning-color { color: #ffc107; }
        .poor { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PHP 8.3 Performance Monitor</h1>
        <p><strong>AWS Elastic Beanstalk - Amazon Linux 2023</strong></p>
        
        <?php
        
        // Get basic PHP information
        $phpVersion = PHP_VERSION;
        $platform = php_uname();
        $memoryLimit = ini_get('memory_limit');
        $maxExecutionTime = ini_get('max_execution_time');
        
        echo "<div class='info'>";
        echo "<h3>📋 Platform Information</h3>";
        echo "<p><strong>PHP Version:</strong> {$phpVersion}</p>";
        echo "<p><strong>Platform:</strong> " . substr($platform, 0, 80) . "</p>";
        echo "<p><strong>Memory Limit:</strong> {$memoryLimit}</p>";
        echo "<p><strong>Max Execution Time:</strong> {$maxExecutionTime}s</p>";
        echo "</div>";
        
        // Check OPcache status
        if (function_exists('opcache_get_status') && function_exists('opcache_get_configuration')) {
            $status = call_user_func('opcache_get_status');
            $config = call_user_func('opcache_get_configuration');
            
            if ($status === false) {
                echo "<div class='error'>";
                echo "<h3>❌ OPcache Not Running</h3>";
                echo "<p>OPcache is not currently active. Enable it for better performance.</p>";
                echo "</div>";
            } else {
                // Calculate metrics
                $hitRate = round($status['opcache_statistics']['opcache_hit_rate'], 2);
                $memoryUsed = round($status['memory_usage']['used_memory'] / 1024 / 1024, 2);
                $memoryTotal = round($status['memory_usage']['free_memory'] / 1024 / 1024, 2) + $memoryUsed;
                $memoryPercent = round(($memoryUsed / $memoryTotal) * 100, 1);
                
                // Determine status classes
                $hitRateClass = $hitRate >= 95 ? 'excellent' : ($hitRate >= 85 ? 'good' : ($hitRate >= 70 ? 'warning-color' : 'poor'));
                $memoryClass = $memoryPercent <= 80 ? 'excellent' : ($memoryPercent <= 90 ? 'good' : 'warning-color');
                
                echo "<div class='success'>";
                echo "<h3>✅ OPcache Status - Active</h3>";
                echo "</div>";
                
                // Key metrics
                echo "<div style='display: flex; flex-wrap: wrap; justify-content: space-around;'>";
                
                echo "<div class='metric'>";
                echo "<div class='metric-value {$hitRateClass}'>{$hitRate}%</div>";
                echo "<div>Hit Rate</div>";
                echo "</div>";
                
                echo "<div class='metric'>";
                echo "<div class='metric-value {$memoryClass}'>{$memoryUsed} MB</div>";
                echo "<div>Memory Used</div>";
                echo "</div>";
                
                echo "<div class='metric'>";
                echo "<div class='metric-value excellent'>" . number_format($status['opcache_statistics']['hits']) . "</div>";
                echo "<div>Cache Hits</div>";
                echo "</div>";
                
                echo "<div class='metric'>";
                echo "<div class='metric-value'>" . number_format($status['opcache_statistics']['num_cached_scripts']) . "</div>";
                echo "<div>Cached Scripts</div>";
                echo "</div>";
                
                echo "</div>";
                
                // JIT Status (PHP 8.3 specific)
                echo "<h3>⚡ JIT Compilation Status</h3>";
                if (isset($status['jit'])) {
                    $jitEnabled = $status['jit']['enabled'] ?? false;
                    $jitBufferSize = $config['directives']['opcache.jit_buffer_size'] ?? 'Not set';
                    
                    if ($jitEnabled) {
                        echo "<div class='success'>";
                        echo "<p><strong>JIT Status:</strong> ✅ Enabled</p>";
                        echo "<p><strong>JIT Buffer Size:</strong> {$jitBufferSize}</p>";
                        if (isset($status['jit']['buffer_size'])) {
                            echo "<p><strong>JIT Buffer Used:</strong> " . round($status['jit']['buffer_size'] / 1024 / 1024, 2) . " MB</p>";
                        }
                        echo "</div>";
                    } else {
                        echo "<div class='warning'>";
                        echo "<p><strong>JIT Status:</strong> ⚠️ Disabled</p>";
                        echo "<p>Enable JIT for additional performance improvements in compute-intensive operations.</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<div class='info'>";
                    echo "<p>JIT information not available in this PHP version.</p>";
                    echo "</div>";
                }
                
                // Detailed statistics
                echo "<h3>📊 Detailed Statistics</h3>";
                echo "<table>";
                echo "<tr><th>Metric</th><th>Value</th><th>Status</th></tr>";
                
                $stats = [
                    ['Hit Rate', $hitRate . '%', $hitRate >= 95 ? '✅ Excellent' : ($hitRate >= 85 ? '✅ Good' : '⚠️ Needs Improvement')],
                    ['Memory Usage', "{$memoryUsed} MB / {$memoryTotal} MB ({$memoryPercent}%)", $memoryPercent <= 80 ? '✅ Good' : '⚠️ Monitor'],
                    ['Cached Scripts', number_format($status['opcache_statistics']['num_cached_scripts']), '✅ Active'],
                    ['Cache Hits', number_format($status['opcache_statistics']['hits']), '✅ Active'],
                    ['Cache Misses', number_format($status['opcache_statistics']['misses']), $status['opcache_statistics']['misses'] < 1000 ? '✅ Low' : '⚠️ High'],
                    ['Restarts', $status['opcache_statistics']['oom_restarts'] + $status['opcache_statistics']['hash_restarts'] + $status['opcache_statistics']['manual_restarts'], $status['opcache_statistics']['oom_restarts'] == 0 ? '✅ Stable' : '⚠️ Check Memory'],
                ];
                
                foreach ($stats as $stat) {
                    echo "<tr>";
                    echo "<td>{$stat[0]}</td>";
                    echo "<td>{$stat[1]}</td>";
                    echo "<td>{$stat[2]}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Configuration details
                echo "<h3>⚙️ Configuration</h3>";
                echo "<table>";
                echo "<tr><th>Setting</th><th>Value</th></tr>";
                
                $configItems = [
                    'opcache.enable' => $config['directives']['opcache.enable'] ? 'Enabled' : 'Disabled',
                    'opcache.memory_consumption' => $config['directives']['opcache.memory_consumption'] . ' bytes',
                    'opcache.max_accelerated_files' => $config['directives']['opcache.max_accelerated_files'],
                    'opcache.revalidate_freq' => $config['directives']['opcache.revalidate_freq'] . ' seconds',
                    'opcache.jit_buffer_size' => $config['directives']['opcache.jit_buffer_size'] ?? 'Not set',
                    'opcache.jit' => $config['directives']['opcache.jit'] ?? 'Not set',
                ];
                
                foreach ($configItems as $key => $value) {
                    echo "<tr>";
                    echo "<td>{$key}</td>";
                    echo "<td>{$value}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Recommendations
                echo "<h3>💡 Performance Recommendations</h3>";
                
                if ($hitRate < 95) {
                    echo "<div class='warning'>";
                    echo "<p><strong>Improve Hit Rate:</strong> Consider increasing opcache.memory_consumption or opcache.max_accelerated_files</p>";
                    echo "</div>";
                }
                
                if ($memoryPercent > 80) {
                    echo "<div class='warning'>";
                    echo "<p><strong>Memory Usage High:</strong> Consider increasing opcache.memory_consumption</p>";
                    echo "</div>";
                }
                
                if (!($status['jit']['enabled'] ?? false)) {
                    echo "<div class='info'>";
                    echo "<p><strong>Enable JIT:</strong> Add opcache.jit_buffer_size=100M and opcache.jit=1255 for additional performance</p>";
                    echo "</div>";
                }
                
                if ($hitRate >= 95 && $memoryPercent <= 80 && ($status['jit']['enabled'] ?? false)) {
                    echo "<div class='success'>";
                    echo "<p><strong>🎉 Excellent Configuration!</strong> Your OPcache is optimally configured for performance.</p>";
                    echo "</div>";
                }
            }
        } else {
            echo "<div class='error'>";
            echo "<h3>❌ OPcache Not Available</h3>";
            echo "<p>OPcache extension is not installed or not available.</p>";
            echo "</div>";
        }
        
        // Performance test
        echo "<h3>🧪 Quick Performance Test</h3>";
        $start = microtime(true);
        $iterations = 100000;
        
        for ($i = 0; $i < $iterations; $i++) {
            $dummy = md5($i);
        }
        
        $end = microtime(true);
        $executionTime = round(($end - $start) * 1000, 2);
        
        echo "<div class='info'>";
        echo "<p><strong>Performance Test:</strong> {$iterations} MD5 operations in {$executionTime}ms</p>";
        
        if ($executionTime < 50) {
            echo "<p class='excellent'>🚀 Excellent performance!</p>";
        } elseif ($executionTime < 100) {
            echo "<p class='good'>✅ Good performance</p>";
        } else {
            echo "<p class='warning-color'>⚠️ Consider optimization</p>";
        }
        echo "</div>";
        
        ?>
        
        <div class="info">
            <h3>🔄 Actions</h3>
            <p><strong>Refresh Status:</strong> <a href="<?php echo $_SERVER['PHP_SELF']; ?>">Reload this page</a></p>
            <p><strong>Clear OPcache:</strong> <a href="?clear=1">Clear OPcache</a> (Use with caution in production)</p>
            
            <?php
            if (isset($_GET['clear']) && $_GET['clear'] == '1') {
                if (function_exists('opcache_reset')) {
                    call_user_func('opcache_reset');
                    echo "<div class='success'>✅ OPcache cleared successfully!</div>";
                } else {
                    echo "<div class='error'>❌ Unable to clear OPcache</div>";
                }
            }
            ?>
        </div>
        
        <div class="info">
            <p><em>Generated on: <?php echo date('Y-m-d H:i:s'); ?></em></p>
            <p><em>For AWS Elastic Beanstalk - PHP 8.3 on Amazon Linux 2023</em></p>
        </div>
    </div>
</body>
</html>
