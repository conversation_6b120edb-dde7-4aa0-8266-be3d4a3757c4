<?php
// This script is designed to be called directly to process data in the background
// It ignores the output and continues processing even if the client disconnects

// Disable error display in the output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Enable error logging
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// OpenAI namespace will be loaded via autoload

// Set maximum execution time to 5 minutes
ini_set('max_execution_time', 300);

// Ignore user aborts - continue processing even if the client disconnects
ignore_user_abort(true);

// Send a response immediately to the client
header('Content-Type: application/json');
echo json_encode(['status' => 'processing', 'message' => 'Processing started in background']);

// Flush output buffers to send the response immediately
if (ob_get_level()) ob_end_flush();
flush();

// Check if data_id is provided
if (!isset($_GET['data_id'])) {
    error_log("No data_id provided to process_background.php");
    exit();
}

$data_id = $_GET['data_id'];
error_log("Starting background processing for data_id: $data_id");

try {
    // Load required files
    require_once 'DatabaseInteraction.php';
    require_once 'vendor/autoload.php';
    
    // Load environment variables if Dotenv is available
    if (class_exists('Dotenv\\Dotenv')) {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
        $dotenv->load();
    }
    
    $db = new DatabaseInteraction();
    $conn = $db->connect();
    
    // Check if summary already exists
    $check_query = "SELECT id FROM feedback_summaries WHERE data_id = :data_id";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(':data_id', $data_id);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        error_log("Summary already exists for data_id: $data_id, skipping summary generation");
        exit();
    }
    
    $feedback_data = $db->getFeedbackDataByDataId($data_id);
    
    if (!$feedback_data) {
        error_log("No feedback data found for data_id: $data_id");
        exit();
    }
    
    // Retrieve user_id associated with the data_id
    $user_id = $feedback_data[0]['user_id'];
    
    // Use all records for summary generation
    $all_feedback = $feedback_data;
    $text_to_analyze = implode("\n", array_column($all_feedback, 'feedback_data'));
    
    // Get the total count of feedback records
    $total_feedback_count = count($feedback_data);
    error_log("Total feedback count for data_id $data_id: $total_feedback_count");
    
    // Get domain category from feedback_data
    $domain_category = !empty($feedback_data[0]['domain_category']) ? $feedback_data[0]['domain_category'] : 'Collections';
    
    // If domain category is not set, use Collections as default
    if (empty($domain_category)) {
        $domain_category = 'Collections';
    }
    
    error_log("Generating summary for data_id: $data_id with domain category: $domain_category");
    
    // Call process_data.php directly with the data_id
    $_GET['data_id'] = $data_id;
    include 'process_data.php';
    
    error_log("Background processing completed for data_id: $data_id");
} catch (Exception $e) {
    error_log("Error in process_background.php: " . $e->getMessage());
}
