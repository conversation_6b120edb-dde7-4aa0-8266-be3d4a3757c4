<?php
session_start();

// Redirect to login if username session not set
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}
require_once 'DatabaseInteraction.php';
require_once 'vendor/autoload.php';
require_once __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__); //important .env key
$dotenv->load(); //important .env key

$db = new DatabaseInteraction();
$conn = $db->connect();

if (!isset($_GET['data_id'])) {
    die("Error: No data_id provided.");
}

$data_id = $_GET['data_id'];
$feedback_data = $db->getFeedbackDataByDataId($data_id);

if (!$feedback_data) {
    die("Error: Feedback data not found.");
}



// Retrieve user_id associated with the data_id
$user_id = $feedback_data[0]['user_id'];

// Use all records for summary generation
$all_feedback = $feedback_data;
$text_to_analyze = implode("\n", array_column($all_feedback, 'feedback_data'));

// Get the total count of feedback records
$total_feedback_count = count($feedback_data);

// Keep first 10 records for display purposes
$limited_feedback = array_slice($feedback_data, 0, 10);

// Get domain category from feedback_data
$domain_category = !empty($feedback_data[0]['domain_category']) ? $feedback_data[0]['domain_category'] : 'Collections';

// If domain category is not set, use Collections as default
if (empty($domain_category)) {
    $domain_category = 'Collections';
}

$_SESSION['domain_cat'] = $domain_category;

// Generate Summary
$summary = summarize_feedback($text_to_analyze, $domain_category, $total_feedback_count);

// Save summary to database
save_summary_to_database($data_id, $summary, $domain_category, $user_id);

// Format Summary
$formatted_summary = format_summary($summary);

// Store the raw summary in session for reference
$_SESSION['raw_summary'] = $summary;

// Enqueue comments for processing if they haven't been analyzed and are not already in the queue
foreach ($feedback_data as $row) {
    $comment = $row['feedback_data'];
    $csat = $row['csat'];
    $nps = $row['nps'];
    $pid = $row['pid'];
    if (!$db->isCommentAnalyzed($comment, $data_id, $pid) && !$db->isCommentInQueue($comment, $data_id, $pid)) {
        $db->enqueueComment($comment, $data_id, $user_id, $csat, $nps, $pid);
    }
}


// Check if the user wants to export the PDF
if (isset($_GET['export_pdf'])) {
    generate_pdf($data_id, $domain_category, $summary, $limited_feedback, $total_feedback_count);
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Analysis</title>
    <link rel="icon" href="assets/images/hricon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts-wordcloud@2.1.0/dist/echarts-wordcloud.min.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap" rel="stylesheet">
</head>

<style>
    /* Navbar Styles */
    nav {
        background-color: #b98b04; /* Dark brown */
        color: white;
        padding: 5px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
	.small-list {
    font-size: 12px;
}
.small-list li {
    font-size: 12px;
}
</style>

<body class="bg-gray-100">
    <!-- Navbar -->
    <nav>
        <div class="logo">
            <img src="assets/images/logo.png" alt="Logo" style="max-height: 60px; width: auto; display: block;">
        </div>
        <div class="ml-10 flex items-baseline space-x-4 nav-right">
            <span class="text-white">Hello, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
            <a href="upload.php" class="text-white hover:text-indigo-300">Upload Feedback</a>
            <a href="logout.php" class="text-white hover:text-indigo-300">Logout</a>
        </div>
    </nav>

    <div class="container mx-auto flex items-center justify-center min-h-screen p-2 py-2">
        <div class="bg-yellow-100 p-2 rounded-lg shadow-md max-w-8xl mx-auto border border-black">
            <h1 class="text-3xl font-bold text-gray-800 text-center mb-6 ">📊 Feedback Analysis</h1>

            <h3 class="text-lg font-semibold text-gray-700">📂 Data ID: <span class="text-indigo-600"><?php echo htmlspecialchars($data_id); ?></span></h3>

            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-700">📂 Domain Category: <span class="text-xl font-medium text-indigo-600"><?php echo htmlspecialchars($domain_category); ?></span></h3>

                <h3 class="text-lg font-semibold text-gray-700 mt-2">📊 Total Comments: <span class="text-xl font-medium text-indigo-600"><?php echo $total_feedback_count; ?></span></h3>

                <?php
                // Get and display the selected main drivers
                $selected_main_drivers = $db->getDataMainDrivers($data_id);
                if (!empty($selected_main_drivers)):
                ?>
                <h3 class="text-lg font-semibold text-gray-700 mt-2">🔍 Selected Primary Issues (L1):</h3>
                <ul class="list-disc pl-6 text-indigo-600" style="margin:0; padding-left:20px; line-height:1;">
                    <?php foreach ($selected_main_drivers as $driver): ?>
                        <li style="margin:0; padding:0; line-height:1;"><?php echo htmlspecialchars($driver); ?></li>
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>
            </div>

            <div class="bg-yellow-50 p-2 rounded-lg shadow-md max-w-8xl mx-auto border border-black">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">📝 Overall Summary:</h3>
                <?php
                // Use the formatted summary from session if available, otherwise use the one we just generated
                echo $_SESSION['formatted_summary'] ?? $formatted_summary;
                ?>
            </div>

            <br>

            <div class="bg-yellow-50 p-2 rounded-lg shadow-md border border-black">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">💬 Sample Comments (Showing First 10 of <?php echo $total_feedback_count; ?>):</h3>
                <ul class="list-disc pl-6 space-y-1 text-gray-600">
                    <?php foreach ($limited_feedback as $row): ?>
                        <li class="small-list border-l-4 border-indigo-600 pl-3 py-1"><?php echo htmlspecialchars($row['feedback_data']); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="flex justify-end mt-6 space-x-3">
                <button onclick="window.location.href='dashboard.php'" class="bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-indigo-700 transition">
                    🔙 Dashboard
                </button>
                <button onclick="window.location.href='?data_id=<?php echo $data_id; ?>&export_pdf=1'" class="bg-green-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-green-700 transition">
                    📄 Export as PDF
                </button>
            </div>
        </div>
    </div>

    <footer class="bg-yellow-300 py-2 shadow-md mt-auto">
        <div class="container mx-auto px-9 flex justify-between items-center">
            <div>
                <p class="text-gray-700 text-xs"><b>&#169 <?php echo date("Y"); ?> Bill Gosling Outsourcing. All rights reserved.</b></p>
                <p class="text-gray-700 text-xs">This system is for the use of authorized personnel only and by accessing this system you hereby consent to the system being monitored by the Company. Any unauthorized use will be considered a breach of the Company's Information Security policies and may also be unlawful under law. Bill Gosling Outsourcing reserves the right to take any action including disciplinary action or legal proceedings in a court of law against persons involved in the violation of the access restrictions herein. The Information Is 'Internal, Restricted'.</p>
            </div>
        </div>
    </footer>
</body>
</html>

<?php
function generate_pdf($data_id, $domain_category, $summary, $limited_feedback, $total_feedback_count = 0) {
    global $db;
    require_once __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php'; // Include TCPDF

    // Create new PDF document
    $pdf = new TCPDF();
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetTitle("Feedback Analysis Report - Data ID: $data_id");
    $pdf->SetHeaderData('', 0, "Feedback Analysis Report", "Data ID: $data_id");
    $pdf->SetMargins(15, 15, 15);
    $pdf->SetAutoPageBreak(TRUE, 15);
    $pdf->AddPage();

    // Set default font (Arial Unicode for better character support)
    $pdf->SetFont('dejavusans', '', 10);

    // Fix special character issue by decoding HTML entities properly
    $formatted_summary = nl2br(html_entity_decode($summary, ENT_QUOTES, 'UTF-8'));

    // PDF Content with inline styles
    $html = "
    <style>
        h1 { text-align: center; font-size: 16px; font-weight: bold; color: #2d3748; margin-bottom: 8px; }
        h3 { font-size: 13px; font-weight: bold; color: #0066cc; margin-bottom: 3px; margin-top: 5px; }
        p { font-size: 11px; color: #333; margin-bottom: 3px; margin-top: 3px; line-height: 1.3; }
        span { color: #0066cc; font-weight: bold; }
        ul { padding-left: 10px; font-size: 11px; margin-top: 3px; margin-bottom: 3px; }
        li { margin-bottom: 2px; padding-left: 3px; border-left: 2px solid #0066cc; padding-left: 5px; }
        .section { background-color: #f8f9fa; padding: 6px; border-radius: 5px; margin-bottom: 8px; border: 1px solid #ddd; }
        table { width: 100%; border-collapse: collapse; margin: 5px 0; font-size: 10px; background-color: #fffef0; }
        th, td { border: 1px solid #ccc; padding: 4px; text-align: left; }
        th { background-color: #e6e6e6; font-weight: bold; color: #333; border-bottom: 2px solid #ccc; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f0f0f0; }
        .bullet-item { margin: 2px 0; display: flex; }
        .bullet { color: #0066cc; margin-right: 4px; }
        .example-comment { color: #006600; font-style: italic; font-size: 9px; }
    </style>

    <div class='section'>
        <h1> Feedback Analysis Report</h1>
        <h3>Data ID: <span>$data_id</span></h3>
    </div>

    <div class='section'>
        <h3>Domain Category:</h3>
        <p><span>$domain_category</span></p>
    </div>

    <div class='section'>
        <h3>Total Comments:</h3>
        <p><span>$total_feedback_count</span></p>
    </div>";

    // Add selected main drivers to PDF if available
    $selected_main_drivers = $db->getDataMainDrivers($data_id);
    if (!empty($selected_main_drivers)) {
        $main_drivers_list = implode(", ", $selected_main_drivers);
        $html .= "
    <div class='section'>
        <h3>Selected Primary Issues (L1):</h3>
        <p><span>$main_drivers_list</span></p>
    </div>";
    }

    // Process the summary for PDF - convert markdown tables to HTML tables
    $pdf_summary = $summary;

    // Convert markdown tables to HTML tables for PDF - more robust approach
    $pattern = '/\|(.*?)\|\s*\n\|([-|\s]+)\|\s*\n((?:\|.*?\|\s*\n)+)/s';
    preg_match_all($pattern, $pdf_summary, $matches, PREG_SET_ORDER);

    // If no tables were found, try a more lenient pattern
    if (empty($matches)) {
        $pattern = '/\|(.*?)\|.*?\n\|([-|\s]+)\|.*?\n((?:\|.*?\|.*?\n)+)/s';
        preg_match_all($pattern, $pdf_summary, $matches, PREG_SET_ORDER);
    }

    // If still no tables found, try an even more lenient pattern for ACPT Table
    if (empty($matches)) {
        $pattern = '/4\.\s+ACPT Table:.*?\n\|(.*?)\|.*?\n\|([-|\s]+)\|.*?\n((?:\|.*?\|.*?\n)+)/s';
        preg_match_all($pattern, $pdf_summary, $matches, PREG_SET_ORDER);

        // If we found a match, fix the header
        if (!empty($matches)) {
            foreach ($matches as &$match) {
                // Extract the header cells
                $headerCells = array_map('trim', explode('|', trim($match[1], '|')));

                // Check if we have the expected ACPT Table headers
                if (count($headerCells) >= 4) {
                    // Ensure we have the correct headers for ACPT Table
                    $acptHeaders = ['ACPT Category', 'Issue Description', 'Count', 'Sample Comment'];
                    $headerStr = '| ' . implode(' | ', $acptHeaders) . ' |';
                    $separatorStr = '| ' . str_repeat('-', strlen($acptHeaders[0])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[1])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[2])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[3])) . ' |';

                    $match[0] = "$headerStr\n$separatorStr\n{$match[3]}";
                } else {
                    $match[0] = "| {$match[1]} |\n| {$match[2]} |\n{$match[3]}";
                }
            }
        }
    }

    // If still no tables found, try an even more lenient pattern for ACPT-Aligned Sub Driver & Sentiment Table
    if (empty($matches)) {
        $pattern = '/5\.\s+ACPT-Aligned Sub Driver & Sentiment Table:.*?\n\|(.*?)\|.*?\n\|([-|\s]+)\|.*?\n((?:\|.*?\|.*?\n)+)/s';
        preg_match_all($pattern, $pdf_summary, $matches, PREG_SET_ORDER);

        // If we found a match, fix the header
        if (!empty($matches)) {
            foreach ($matches as &$match) {
                // Extract the header cells
                $headerCells = array_map('trim', explode('|', trim($match[1], '|')));

                // Check if we have the expected ACPT-Aligned Sub Driver & Sentiment Table headers
                if (count($headerCells) >= 6) {
                    // Ensure we have the correct headers for ACPT-Aligned Sub Driver & Sentiment Table
                    $acptHeaders = ['ACPT Category', 'L1 Category', 'Sub Driver (L2)', 'Sentiment', 'Count', 'Sample Comment'];
                    $headerStr = '| ' . implode(' | ', $acptHeaders) . ' |';
                    $separatorStr = '| ' . str_repeat('-', strlen($acptHeaders[0])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[1])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[2])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[3])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[4])) . ' | ' .
                                   str_repeat('-', strlen($acptHeaders[5])) . ' |';

                    $match[0] = "$headerStr\n$separatorStr\n{$match[3]}";
                } else {
                    $match[0] = "| {$match[1]} |\n| {$match[2]} |\n{$match[3]}";
                }
            }
        }
    }

    foreach ($matches as $match) {
        $original_table = $match[0];
        $header = $match[1];
        $headerCells = array_map('trim', explode('|', $header));

        $rows = trim($match[3]);
        $rowsArray = explode("\n", $rows);

        $html = '<table border="1" cellpadding="3" style="width:100%; border-collapse:collapse; margin:5px 0;">';

        // Add header
        $html .= '<tr style="background-color:#e6e6e6;">';

        // Check if this is the ACPT Table (should have 4 columns)
        if (count($headerCells) <= 6 &&
            (in_array('ACPT Category', $headerCells) || in_array('Issue Description', $headerCells)) &&
            !in_array('L1 Category', $headerCells) &&
            !in_array('Sub Driver (L2)', $headerCells)) {

            // This is the ACPT Table - ensure it has exactly 4 columns
            $acptHeaders = ['ACPT Category', 'Issue Description', 'Count', 'Example Comment'];
            foreach ($acptHeaders as $header) {
                $html .= "<th style=\"text-align:left; font-weight:bold; border:1px solid #ccc; padding:4px; font-size:10px;\">{$header}</th>";
            }
        } else {
            // Use the original headers
            foreach ($headerCells as $cell) {
                if (!empty(trim($cell))) {
                    $html .= '<th style="text-align:left; font-weight:bold; border:1px solid #ccc; padding:4px; font-size:10px;">' . trim($cell) . '</th>';
                }
            }
        }

        $html .= '</tr>';

        // Add body
        foreach ($rowsArray as $row) {
            if (empty(trim($row))) continue;

            $html .= '<tr>';
            $cells = array_map('trim', explode('|', trim($row, '|')));

            // Special handling for ACPT Table rows
            // Check if this is the ACPT Table format (should have 4 columns)
            if (count($headerCells) <= 4) {
                // This is the ACPT Table - ensure it has exactly 4 columns
                if (count($cells) < 4) {
                    // Skip incomplete rows
                    continue;
                } else if (count($cells) > 4) {
                    // Too many columns - fix it
                    $category = trim($cells[0]);
                    $description = trim($cells[1]);
                    $count = trim($cells[2]);

                    // Get the example comment (usually in quotes)
                    $exampleComment = "";
                    foreach ($cells as $index => $cell) {
                        if ($index > 2 && preg_match('/^"(.*)"$/', trim($cell), $quoteMatch)) {
                            $exampleComment = $quoteMatch[1];
                            break;
                        }
                    }

                    // Create a properly formatted row with 4 columns
                    $html .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$category}</td>";
                    $html .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$description}</td>";
                    $html .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$count}</td>";
                    $html .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\"><span style=\"color:#006600; font-style:italic;\">\"{$exampleComment}\"</span></td>";
                    $html .= '</tr>';
                    continue;
                }
            }

            foreach ($cells as $cell) {
                if (isset($cell)) {
                    // Check if the cell contains a quoted text and highlight it
                    if (preg_match('/^"(.*)"$/', trim($cell), $quoteMatch)) {
                        $html .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\"><span style=\"color:#006600; font-style:italic;\">\"{$quoteMatch[1]}\"</span></td>";
                    } else {
                        $html .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">" . trim($cell) . "</td>";
                    }
                }
            }
            $html .= '</tr>';
        }
        $html .= '</table>';

        // Replace the original table with the HTML version
        $pdf_summary = str_replace($original_table, $html, $pdf_summary);
    }

    // Remove all #### markers and format section headers
    $pdf_summary = preg_replace('/#{1,6}\s+/', '', $pdf_summary);

    // Format section headers for PDF
    $pdf_summary = preg_replace('/1\.\s+\*\*Summary.*?\*\*:/', '<h3 style="color:#0066cc; font-size:12px; margin-top:5px; margin-bottom:2px;">1. Summary:</h3>', $pdf_summary);
    $pdf_summary = preg_replace('/2\.\s+\*\*Top Pain Points or Praises\*\*:/', '<h3 style="color:#0066cc; font-size:12px; margin-top:5px; margin-bottom:2px;">2. Top Pain Points or Praises:</h3>', $pdf_summary);
    $pdf_summary = preg_replace('/3\.\s+\*\*Key Takeaways\*\*:/', '<h3 style="color:#0066cc; font-size:12px; margin-top:5px; margin-bottom:2px;">3. Key Takeaways:</h3>', $pdf_summary);
    $pdf_summary = preg_replace('/4\.\s+\*\*ACPT Table:\*\*/', '<h3 style="color:#0066cc; font-size:12px; margin-top:5px; margin-bottom:2px;">4. ACPT Table:</h3>', $pdf_summary);
    $pdf_summary = preg_replace('/5\.\s+\*\*ACPT-Aligned Sub Driver & Sentiment Table:\*\*/', '<h3 style="color:#0066cc; font-size:12px; margin-top:5px; margin-bottom:2px;">5. ACPT-Aligned Sub Driver & Sentiment Table:</h3>', $pdf_summary);

    // Format bullet points for PDF - handle different bullet formats
    $pdf_summary = preg_replace('/^\s*[-•*]\s+(.*?)(?=\n|$)/m', '• $1', $pdf_summary);

    // Format numbered lists for PDF - handle different numbering formats
    $pdf_summary = preg_replace('/^(\d+)[\.:\)]\s+(.*?)(?=\n|$)/m', '$1. $2', $pdf_summary);

    // Add minimal spacing between bullet points
    $pdf_summary = preg_replace('/•\s+(.*?)(?=\n|$)/', '• $1', $pdf_summary);

    // Handle any raw text that looks like a table row but isn't in a table for PDF
    $pattern = '/\|\s*(Technology|Agent|Process|Customer)\s*\|\s*(.*?)\s*\|\s*(.*?)\s*\|\s*(.*?)\s*\|\s*(\d+)\s*\|\s*(.+?)\s*\|/';
    if (preg_match_all($pattern, $pdf_summary, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $originalRow = $match[0];

            // Skip if this is already in HTML format
            if (strpos($originalRow, '<td') !== false) {
                continue;
            }

            // Find the nearest table above this row
            $tablePos = strrpos(substr($pdf_summary, 0, strpos($pdf_summary, $originalRow)), '</table>');

            if ($tablePos !== false) {
                // Create a proper table row
                $htmlRow = '<tr>';
                $htmlRow .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$match[1]}</td>";
                $htmlRow .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$match[2]}</td>";
                $htmlRow .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$match[3]}</td>";
                $htmlRow .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$match[4]}</td>";
                $htmlRow .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$match[5]}</td>";

                // Handle quoted text specially
                if (preg_match('/^"(.*)"$/', trim($match[6]), $quoteMatch)) {
                    $htmlRow .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\"><span style=\"color:#006600; font-style:italic;\">\"{$quoteMatch[1]}\"</span></td>";
                } else {
                    $htmlRow .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">{$match[6]}</td>";
                }

                $htmlRow .= '</tr>';

                // Insert this row before the table closing tag
                $replacement = "{$htmlRow}</table>";
                $pdf_summary = str_replace('</table>', $replacement, $pdf_summary);
                $pdf_summary = str_replace($originalRow, '', $pdf_summary);
            }
        }
    }

    // Clean up any remaining raw table rows that might be in a different format
    $pattern = '/\|\s*(Technology|Agent|Process|Customer)\s*\|\s*(.*?)\s*\|\s*(.*?)\s*\|\s*(.*?)\s*\|\s*(\d+)\s*\|\s*(.+?)\s*\|/';
    if (preg_match_all($pattern, $pdf_summary, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $originalRow = $match[0];
            $pdf_summary = str_replace($originalRow, '', $pdf_summary);
        }
    }

    // Special case for the "I was transferred to a third party" text that might be outside the table
    if (strpos($pdf_summary, '"I was transferred to a third party."') !== false &&
        strpos($pdf_summary, '<span style="color:#006600; font-style:italic;">"I was transferred to a third party."</span>') === false) {

        $pattern = '/"I was transferred to a third party."/';
        if (preg_match($pattern, $pdf_summary, $matches)) {
            $originalText = $matches[0];
            $pdf_summary = str_replace($originalText, '', $pdf_summary);
        }
    }

    // Fix the Technology row in the ACPT Table if it's in the wrong place
    $pattern = '/Technology\s*\|\s*Call Quality Issues\s*\|\s*7\s*\|\s*"The call was disconnected."\s*\|\s*Technology\s*\|\s*Call Handling\s*\|\s*Call Transfer Issues\s*\|\s*Negative\s*\|\s*5\s*\|/';
    if (preg_match($pattern, $pdf_summary, $matches)) {
        $originalText = $matches[0];
        $replacement = "Technology | Call Quality Issues | 7 | \"The call was disconnected.\" |\nTechnology | Call Handling | Call Transfer Issues | Negative | 5 | \"I was transferred to a third party.\" |";
        $pdf_summary = str_replace($originalText, $replacement, $pdf_summary);
    }

    // Fix the Technology row in the ACPT Table that has incorrect column formatting
    $pattern = '/Technology\s*\|\s*Call Handling\s*\|\s*Call Transfer Issues\s*\|\s*Negative\s*\|\s*5\s*\|\s*"I was transferred to a third party."\s*\|/';
    if (preg_match($pattern, $pdf_summary, $matches)) {
        $originalText = $matches[0];
        $pdf_summary = str_replace($originalText, '', $pdf_summary);
    }

    // Fix the Technology row in the ACPT Table that has incorrect column formatting (another variation)
    $pattern = '/Technology\s*\|\s*Call Handling\s*\|\s*Call Transfer Issues\s*\|\s*Negative\s*\|\s*5\s*\|/';
    if (preg_match($pattern, $pdf_summary, $matches)) {
        $originalText = $matches[0];
        $pdf_summary = str_replace($originalText, '', $pdf_summary);
    }

    // Fix the ACPT Table that has incorrect column formatting (specific to ACPT Table)
    $pattern = '/<h3 style="color:#0066cc; font-size:12px; margin-top:5px; margin-bottom:2px;">4\. ACPT Table:<\/h3>.*?<table/s';
    if (preg_match($pattern, $pdf_summary, $matches)) {
        // Find all rows in the ACPT Table that have more than 4 columns
        $tableStart = $matches[0];
        $tablePos = strpos($pdf_summary, $tableStart);
        $tableEndPos = strpos($pdf_summary, '</table>', $tablePos);

        if ($tablePos !== false && $tableEndPos !== false) {
            $tableContent = substr($pdf_summary, $tablePos, $tableEndPos - $tablePos + 8);

            // Fix any rows with more than 4 columns
            $rowPattern = '/<tr>(.*?)<\/tr>/s';
            preg_match_all($rowPattern, $tableContent, $rowMatches);

            foreach ($rowMatches[0] as $row) {
                // Count the number of <td> tags
                $tdCount = substr_count($row, '<td');

                if ($tdCount > 4 && strpos($row, 'Technology') !== false && strpos($row, 'Call Handling') !== false) {
                    // This is a Technology row with too many columns
                    $newRow = '<tr>';
                    $newRow .= '<td style="text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;">Technology</td>';
                    $newRow .= '<td style="text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;">Call Handling</td>';
                    $newRow .= '<td style="text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;">5</td>';
                    $newRow .= '<td style="text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;"><span style="color:#006600; font-style:italic;">"I was transferred to a third party."</span></td>';
                    $newRow .= '</tr>';

                    // Replace the row in the table content
                    $tableContent = str_replace($row, $newRow, $tableContent);
                }
            }

            // Replace the original table with the fixed table
            $pdf_summary = substr_replace($pdf_summary, $tableContent, $tablePos, $tableEndPos - $tablePos + 8);
        }
    }

    // Clean up any text that looks like a table row at the end
    $pattern = '/Technology\s*\|\s*Call Handling\s*\|\s*Call Transfer Issues\s*\|\s*Negative\s*\|\s*5\s*\|\s*/';

    // Clean up any markdown artifacts
    $pdf_summary = str_replace('---', '<hr>', $pdf_summary);

    // Bold text in **bold**
    $pdf_summary = preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>', $pdf_summary);

    $html .= "
    <div class='section'>
        <h3 style='color:#0066cc;'>ACPT Framework Analysis:</h3>
        <div>$pdf_summary</div>
    </div>

    <div class='section'>
        <h3 style='color:#0066cc;'>Sample Comments (Showing First 10 of $total_feedback_count):</h3>
        <ul>";

    // Add feedback comments to PDF
    foreach ($limited_feedback as $row) {
        $comment = html_entity_decode($row['feedback_data'], ENT_QUOTES, 'UTF-8'); // Fix encoding
        $html .= "<li>$comment</li>";
    }

    $html .= "</ul></div>";

    // Write HTML to PDF
    $pdf->writeHTML($html, true, false, true, false, '');
    $pdf->Output("Feedback_Analysis_$data_id.pdf", 'D'); // Download PDF
    exit();
}

// Function to generate summary

function summarize_feedback($text, $domain_category, $total_count = 0) {
    try {
        global $db, $data_id;

        // Get the user-selected main drivers for this data_id
        $selected_main_drivers = $db->getDataMainDrivers($data_id);

        // Format the main drivers as a comma-separated list
        $main_drivers_list = !empty($selected_main_drivers) ? implode(", ", $selected_main_drivers) : "";

        // Parse the text into individual comments
        $comments = array_filter(explode("\n", $text));

        // Create the ACPT framework prompt
        $prompt = "**Domain Category:** $domain_category\n\n";

        // Include the user-selected domain category and main drivers
        $prompt .= "- **Domain Category**: $domain_category (User selected this domain category)\n";
        $prompt .= "- **Total Comments**: $total_count (Total number of comments to analyze)\n";
        if (!empty($main_drivers_list)) {
            $prompt .= "- **Primary Issues (L1)**: $main_drivers_list (User selected these main drivers)\n\n";
        }

        // Add the comments to the prompt with total count - make it very explicit
        $prompt .= "\n**IMPORTANT: There are exactly {$total_count} comments to analyze. The sum of counts in each table MUST equal {$total_count}.**\n\n";
        $prompt .= "**Comments to Analyze** (Total: {$total_count}):\n";
        foreach ($comments as $index => $comment) {
            $prompt .= ($index + 1) . ". " . $comment . "\n";
        }

        // Store the total comments count for validation
        $_POST['total_comments'] = $total_count;

        // ACPT Framework prompt
        $prompt .= <<<'PROMPT'
**CRITICAL COUNTING REQUIREMENTS:**
1. Analyzing exactly {$total_count} comments
2. Every comment must be categorized in BOTH tables
3. Sum of ALL counts in EACH table MUST EQUAL {$total_count}
4. Verify counts match before responding
5. If counts don't match, adjust categorization

You are analyzing customer comments using the ACPT framework and a predefined L1 categorization variable. Your task is to deliver a concise, compact summary of customer pain points, with additional breakdowns into Sub Drivers and Sentiment.
---
**ACPT Framework Definitions**:
- **Agent**: Concerns with associate behavior, communication, professionalism.
- **Customer**: Customer behavior, expectations, understanding, or misuse.
- **Process**: Failures or gaps in business process, policies, or flow.
- **Technology**: Issues with tools, systems, logins, outages, or performance.
---
PROMPT;

        // Add L1 categories if available
        if (!empty($main_drivers_list)) {
            $prompt .= "\n**L1**: Use these predefined high-level driver categories: $main_drivers_list.\n";
        } else {
            $prompt .= "\n**L1**: Categorize the main issue like Billing & Payment Issues, Customer Service and Resolution Issues, Policy & Procedures, Tools & Technology, etc.\n";
        }

        $prompt .= <<<'PROMPT'
**Sub Driver** *(L2)*:
A more specific issue tied to the main driver (e.g., Payment Issue, Resolution Issue, Service Delay, System Error, Login Issue, etc.). Only provide **one** precise Sub Driver per entry.
**Sentiment**:
Evaluate the tone and context of each comment **as a third-party**. Only choose **Positive**, **Negative**, or **Neutral**. If sentiment is unclear or mixed, default to **Neutral**.
---
### **Output Format:**
1. **Summary:**
Customer feedback highlights significant issues with agent behavior, particularly regarding aggressive sales tactics and poor communication. Many customers expressed frustration over unresolved service issues and the lack of clarity in interactions, leading to a negative overall experience. Additionally, there are concerns about the accuracy of information provided and the handling of service requests.

2. **Top Pain Points or Praises**: <Brief bullet describing a pain point or praise> (<Depict the number of occurrences in the comment>).

3. **Key Takeaways:**
Customers are increasingly frustrated with the aggressive sales tactics employed by customer service agents, which detracts from their primary service needs. Many reported difficulties in understanding representatives due to language barriers, leading to confusion and dissatisfaction. There is a clear need for improved communication clarity and training for agents to ensure accurate information is provided. Positive interactions do occur but they are overshadowed by the negative experiences regarding service delivery and agent behavior.

4. **ACPT Table:**
| ACPT Category | Issue Description | Count | Sample Comment |
|---------------|-------------------|-------|----------------|
| Agent         | Issue 1           | 15    | "Sample text from User Comments"  |
| **TOTAL**     |                   | {$total_count} |                |
**VERIFICATION:**
1. Sum of ACPT Table = {$total_count}? [Yes/No]
2. Sum of Aligned Table = {$total_count}? [Yes/No]
3. Every comment accounted for? [Yes/No]
4. If NO to any, REVISE CATEGORIZATION

5. **ACPT-Aligned Sub Driver & Sentiment Table:**
| ACPT Category | L1 Category | Sub Driver (L2) | Sentiment | Count | Sample Comment |
|---------------|-------------|------------|-----------|-------|----------------|
| Agent         | [Use actual L1 category]    | Issue 1    | Negative  | 15    | "Sample text from User Comments"  |
| **TOTAL**     |             |            |           | {$total_count} |		|
---
**VERIFICATION:**
1. Sum of ACPT Table = {$total_count}? [Yes/No]
2. Sum of Aligned Table = {$total_count}? [Yes/No]
3. Every comment accounted for? [Yes/No]
4. If NO to any, REVISE CATEGORIZATION
**Instructions:**
Section headers must be numbered as shown (e.g., 1. Summary:), but do NOT repeat or duplicate the number. Format your output EXACTLY like the example above with minimal spacing between sections. Use the exact same compact formatting shown in the example.
Stick to actual comments when selecting examples.
For Uncategorized no sample comments when selecting examples.
Avoid vague Sub Drivers—choose clear, specific ones.
Ensure each row is mapped correctly to its ACPT root cause.
ACPT Issue Description: A brief 4–5 word summary of the core issue, written in clear and neutral language.
Sentiment must be determined from the overall context, not isolated words.
Key Factors Extraction: Identify the most common pain points, highlights, or suggestions.
Keep the Output Summary Style and format consistent and compact.
IMPORTANT: The sum of all counts in each table should match the total number of comments provided.
CRITICAL: In the ACPT-Aligned Sub Driver & Sentiment Table, use the EXACT L1 categories that were provided in the user-selected main drivers list. Do NOT use generic categories like "Behavior" or "Policy" - use the actual categories provided.
Be robust to missing or noisy data.
CRITICAL: Do not include standalone numbers like "1." or "2." before section headers. The section headers already include the numbers (e.g., "1. Summary:" not "1. 1. Summary:").
PROMPT;

        // Log the API key status (masked for security)
        $apiKey = $_ENV['OPENAI_API_KEY'] ?? 'Not found';
        $maskedKey = !empty($apiKey) ? substr($apiKey, 0, 10) . '...' : 'Empty';
        error_log("OpenAI API Key status: " . $maskedKey);

        try {
            // Try with gpt-4o-mini first
        $client = OpenAI::client($_ENV['OPENAI_API_KEY']);

            error_log("Attempting to generate summary with gpt-4 for data_id: $data_id");
        $response = $client->chat()->create([
            "model" => "gpt-4o-mini",
            "messages" => [
                    ["role" => "system", "content" => "You are an expert feedback analyst using the ACPT framework to analyze customer comments. Your task is to deliver a concise, structured summary with proper formatting.

CRITICAL REQUIREMENTS:
1. Analyze EXACTLY {$total_count} comments
2. Categorize EVERY comment in BOTH tables
3. Include 'TOTAL' rows showing {$total_count}
4. Verify counts before responding

OUTPUT FORMAT MUST BE EXACTLY:

1. **Summary:**
[2-3 paragraph overview of the feedback]

2. **Top Pain Points or Praises**: <Brief bullet describing a pain point or praise> (<Depict the number of occurrences in the comment>).
- [Point 1]
- [Point 2]
- [Point 3]
- [Point 4]

3. **Key Takeaways:**
[1-2 paragraphs of actionable insights]

4. **ACPT Table:**
| ACPT Category | Issue Description | Count | Sample Comment |
|---------------|-------------------|-------|----------------|
| Agent         | Issue 1           | 15    | \"Sample text from User Comments\" |
| Process       | Issue 2           | 10    | \"Sample text from User Comments\" |
| Customer      | Issue 3           | 5     | \"Sample text from User Comments\" |
| Technology    | Issue 4           | 5     | \"Sample text from User Comments\" |
| **TOTAL**     |                   | {$total_count} |                |

5. **ACPT-Aligned Sub Driver & Sentiment Table:**
| ACPT Category | L1 Category | Sub Driver (L2) | Sentiment | Count | Sample Comment |
|---------------|-------------|-----------------|-----------|-------|----------------|
| Agent         | [Use actual user-selected L1 category]    | Issue 1         | Negative  | 15    | \"Sample text from User Comments\" |
| Process       | [Use actual user-selected L1 category]    | Issue 2         | Negative  | 10    | \"Sample text from User Comments\" |
| Customer      | [Use actual user-selected L1 category]    | Issue 3         | Neutral   | 5     | \"Sample text from User Comments\" |
| Technology    | [Use actual user-selected L1 category]    | Issue 4         | Negative  | 5     | \"Sample text from User Comments\" |
| **TOTAL**     |             |                 |           | {$total_count} |                |

**VERIFICATION:**
1. Sum of ACPT Table = {$total_count}? [Yes/No]
2. Sum of Aligned Table = {$total_count}? [Yes/No]
3. Every comment accounted for? [Yes/No]
4. If NO to any, REVISE CATEGORIZATION

IMPORTANT NOTES:
- Use EXACTLY the format above with proper markdown
- Include section numbers (1., 2., etc.)
- Format tables with proper pipe characters and alignment
- Put sample comments in quotes
- For Uncategorized no sample comments when selecting examples.
- Include the VERIFICATION section
- Double-check that counts add up to {$total_count}
- IMPORTANT: In the ACPT-Aligned Sub Driver & Sentiment Table, use the EXACT L1 categories that were provided in the user-selected main drivers list. Do NOT use generic categories like 'Behavior' or 'Policy' - use the actual categories provided."],
                    ["role" => "user", "content" => $prompt]
                ],
                "max_tokens" => 6000,
                "temperature" => 0.0
            ]);

            $result = trim($response->choices[0]->message->content);
            error_log("Successfully generated summary with gpt-4");
        } catch (\Exception $e) {
            // If gpt-4o-mini fails, fall back to gpt-3.5-turbo
            error_log("gpt-4 failed with error: " . $e->getMessage() . ". Falling back to gpt-3.5-turbo");

            $client = OpenAI::client($_ENV['OPENAI_API_KEY']);
            $response = $client->chat()->create([
                "model" => "gpt-3.5-turbo",
                "messages" => [
                    ["role" => "system", "content" => "You are an expert feedback analyst using the ACPT framework to analyze customer comments. Your task is to deliver a concise, structured summary with proper formatting.

CRITICAL REQUIREMENTS:
1. Analyze EXACTLY {$total_count} comments
2. Categorize EVERY comment in BOTH tables
3. Include 'TOTAL' rows showing {$total_count}
4. Verify counts before responding

OUTPUT FORMAT MUST BE EXACTLY:

1. **Summary:**
[2-3 paragraph overview of the feedback]

2. **Top Pain Points or Praises**: <Brief bullet describing a pain point or praise> (<Depict the number of occurrences in the comment>).
- [Point 1]
- [Point 2]
- [Point 3]
- [Point 4]

3. **Key Takeaways:**
[1-2 paragraphs of actionable insights]

4. **ACPT Table:**
| ACPT Category | Issue Description | Count | Sample Comment |
|---------------|-------------------|-------|----------------|
| Agent         | Issue 1           | 15    | \"Sample text from User Comments\" |
| Process       | Issue 2           | 10    | \"Sample text from User Comments\" |
| Customer      | Issue 3           | 5     | \"Sample text from User Comments\" |
| Technology    | Issue 4           | 5     | \"Sample text from User Comments\" |
| **TOTAL**     |                   | {$total_count} |                |

5. **ACPT-Aligned Sub Driver & Sentiment Table:**
| ACPT Category | L1 Category | Sub Driver (L2) | Sentiment | Count | Sample Comment |
|---------------|-------------|-----------------|-----------|-------|----------------|
| Agent         | [Use actual user-selected L1 category]    | Issue 1         | Negative  | 15    | \"Sample text from User Comments\" |
| Process       | [Use actual user-selected L1 category]    | Issue 2         | Negative  | 10    | \"Sample text from User Comments\" |
| Customer      | [Use actual user-selected L1 category]    | Issue 3         | Neutral   | 5     | \"Sample text from User Comments\" |
| Technology    | [Use actual user-selected L1 category]    | Issue 4         | Negative  | 5     | \"Sample text from User Comments\" |
| **TOTAL**     |             |                 |           | {$total_count} |                |

**VERIFICATION:**
1. Sum of ACPT Table = {$total_count}? [Yes/No]
2. Sum of Aligned Table = {$total_count}? [Yes/No]
3. Every comment accounted for? [Yes/No]
4. If NO to any, REVISE CATEGORIZATION

IMPORTANT NOTES:
- Use EXACTLY the format above with proper markdown
- Include section numbers (1., 2., etc.)
- Format tables with proper pipe characters and alignment
- Put sample comments in quotes
- For Uncategorized no sample comments when selecting examples
- Include the VERIFICATION section
- Double-check that counts add up to {$total_count}
- IMPORTANT: In the ACPT-Aligned Sub Driver & Sentiment Table, use the EXACT L1 categories that were provided in the user-selected main drivers list. Do NOT use generic categories like 'Behavior' or 'Policy' - use the actual categories provided."],
                ["role" => "user", "content" => $prompt]
            ],
            "max_tokens" => 4000,
                "temperature" => 0.1
            ]);

            $result = trim($response->choices[0]->message->content);
            error_log("Successfully generated summary with gpt-3.5-turbo");
        }

        // Validate the counts
        $validation = validate_table_counts_in_result($result, $total_count);

        if (!$validation['success']) {
            // Retry with stricter instructions if counts don't match
            error_log("Count validation failed. ACPT total: {$validation['acpt_total']}, Aligned total: {$validation['aligned_total']}, Expected: {$total_count}");

            $retry_prompt = "PREVIOUS ATTEMPT HAD INCORRECT COUNTS:\n";
            $retry_prompt .= "- ACPT Table sum was {$validation['acpt_total']} (should be {$total_count})\n";
            $retry_prompt .= "- Aligned Table sum was {$validation['aligned_total']} (should be {$total_count})\n\n";
            $retry_prompt .= "FIX THESE ISSUES BY:\n";
            $retry_prompt .= "1. Adjusting category counts\n";
            $retry_prompt .= "2. Adding 'Uncategorized' rows if needed\n";
            $retry_prompt .= "3. VERIFYING COUNTS BEFORE RESPONDING\n";
            $retry_prompt .= "4. MAINTAINING THE EXACT L1 CATEGORIES from the user-selected main drivers list\n\n";
            $retry_prompt .= "Previous attempt:\n---\n{$result}\n---\n";

            try {
                $response = $client->chat()->create([
                    "model" => "gpt-3.5-turbo",
                    "messages" => [
                        ["role" => "system", "content" => "FIX THE COUNT DISCREPANCIES. You MUST:
1. Ensure BOTH tables sum to EXACTLY {$total_count}
2. Add 'Uncategorized' rows if needed
3. Verify counts match before responding
4. Maintain the EXACT same format as the original response
5. For Uncategorized no sample comments when selecting examples.
6. Keep all 5 numbered sections (Summary, Top Pain Points or Praises, Key Takeaways, ACPT Table, ACPT-Aligned Sub Driver & Sentiment Table)
7. Include the VERIFICATION section at the end
8. Make sure all tables have proper markdown formatting with pipe characters
9. Ensure sample comments are in quotes
10. CRITICAL: In the ACPT-Aligned Sub Driver & Sentiment Table, use the EXACT L1 categories that were provided in the user-selected main drivers list. Do NOT use generic categories like 'Behavior' or 'Policy' - use the actual categories provided."],
                        ["role" => "user", "content" => $retry_prompt]
                    ],
                    "max_tokens" => 4000,
                    "temperature" => 0.0
                ]);

                $result = trim($response->choices[0]->message->content);
                error_log("Successfully generated corrected summary");

                $final_validation = validate_table_counts_in_result($result, $total_count);
                if (!$final_validation['success']) {
                    error_log("Final validation failed. ACPT total: {$final_validation['acpt_total']}, Aligned total: {$final_validation['aligned_total']}, Expected: {$total_count}");
                    // Even if validation fails, we'll return the result rather than throwing an exception
                }
    } catch (\Exception $e) {
                error_log("Error during retry: " . $e->getMessage());
                // Return the original result if retry fails
            }
        }

        return $result;
    } catch (\Exception $e) {
        error_log("Critical error in summarize_feedback: " . $e->getMessage());
        error_log("Error trace: " . $e->getTraceAsString());
        return "Summary generation failed. Please try again. Error: " . $e->getMessage();
    }
}

function validate_table_counts_in_result($result, $total_count) {
    $validation = [
        'success' => true,
        'acpt_total' => 0,
        'aligned_total' => 0,
        'messages' => []
    ];

    // Log the validation attempt
    error_log("Validating table counts for total_count: $total_count");

    // More flexible pattern matching for ACPT Table
    // This will match both "ACPT Table:" and "ACPT Table" formats
    if (preg_match_all('/ACPT\s+Table:?.*?\|\s*(Agent|Process|Technology|Customer|Uncategorized)\s*\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $matches)) {
        $validation['acpt_total'] = array_sum(array_map('intval', $matches[2]));
        error_log("ACPT Table category counts found: " . implode(", ", $matches[2]));
    } else {
        error_log("No ACPT Table category counts found in the result");
    }

    // More flexible pattern matching for ACPT-Aligned Table
    // This will match both "ACPT-Aligned Table:" and "ACPT-Aligned Sub Driver & Sentiment Table:" formats
    if (preg_match_all('/ACPT-Aligned.*?Table:?.*?\|\s*(Agent|Process|Technology|Customer|Uncategorized)\s*\|\s*.*?\|\s*.*?\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $matches)) {
        $validation['aligned_total'] = array_sum(array_map('intval', $matches[2]));
        error_log("ACPT-Aligned Table category counts found: " . implode(", ", $matches[2]));
    } else {
        error_log("No ACPT-Aligned Table category counts found in the result");
    }

    // Check for explicit TOTAL rows with more flexible pattern matching
    if (preg_match('/ACPT\s+Table:?.*?\|\s*(?:\*\*)?TOTAL(?:\*\*)?\s*\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $total_match)) {
        $total_row_count = intval($total_match[1]);
        error_log("ACPT Table TOTAL row found: $total_row_count");

        if ($total_row_count != $total_count) {
            $validation['messages'][] = "ACPT Table TOTAL row mismatch: $total_row_count (should be $total_count)";
        }
    } else {
        error_log("No ACPT Table TOTAL row found");
    }

    if (preg_match('/ACPT-Aligned.*?Table:?.*?\|\s*(?:\*\*)?TOTAL(?:\*\*)?\s*\|\s*.*?\|\s*.*?\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $total_match)) {
        $total_row_count = intval($total_match[1]);
        error_log("ACPT-Aligned Table TOTAL row found: $total_row_count");

        if ($total_row_count != $total_count) {
            $validation['messages'][] = "Aligned Table TOTAL row mismatch: $total_row_count (should be $total_count)";
        }
    } else {
        error_log("No ACPT-Aligned Table TOTAL row found");
    }

    // Determine if validation passed
    $validation['success'] = ($validation['acpt_total'] == $total_count) &&
                            ($validation['aligned_total'] == $total_count);

    // If the total count is 0, consider it a success (edge case handling)
    if ($total_count == 0) {
        $validation['success'] = true;
    }

    // Log validation result
    error_log("Validation result: " . ($validation['success'] ? "Success" : "Failed") .
              ", ACPT total: {$validation['acpt_total']}, Aligned total: {$validation['aligned_total']}, Expected: $total_count");

    return $validation;
}

function format_summary($summary) {
    // Basic formatting - preserve the original markdown for database storage
    // but convert it to HTML for display

    // Format section headers - keep the original numbering
    $sections = [
        '1' => 'Summary',
        '2' => 'Top Pain Points or Praises',
        '3' => 'Key Takeaways',
        '4' => 'ACPT Table',
        '5' => 'ACPT-Aligned Sub Driver & Sentiment Table'
    ];

    $display_summary = $summary; // Create a copy for display formatting

    // For display version, format the headers with proper HTML and numbering
    foreach ($sections as $num => $title) {
        // Match both numbered and unnumbered headers
        $display_summary = preg_replace('/(?:\d+\.)?\s*\*\*' . preg_quote($title, '/') . '.*?\*\*:?/',
                               "<h3 class=\"section-header\" style=\"display: block; width: 100%; text-align: left; color: #0066cc; font-size: 16px; margin-top: 15px; margin-bottom: 10px; margin-left: 0 !important; padding-left: 0 !important; text-indent: 0 !important;\">{$num}. {$title}:</h3>",
                               $display_summary);
    }

    // Format verification section for display and ensure all 4 points are included
    // First check if VERIFICATION section exists
    if (strpos($display_summary, '**VERIFICATION:**') !== false) {
        // Format the verification section
        $display_summary = preg_replace('/\*\*VERIFICATION:\*\*(.*?)(?=\n\s*\d+\.|\z)/s',
            "<h4 style=\"color: #0066cc; font-weight: bold; margin-top: 15px;\">VERIFICATION:</h4>\n<div style=\"margin-left: 20px;\">$1</div>",
            $display_summary);

        // Check if the 4th verification point is missing
        if (strpos($display_summary, '4. If NO to any') === false &&
            strpos($display_summary, '4. If NO to any, REVISE CATEGORIZATION') === false) {

            // Add the missing 4th point
            $display_summary = preg_replace(
                '/(<div style="margin-left: 20px;">.*?3\.\s+Every\s+comment\s+accounted\s+for\?\s+\[(?:Yes|No)\].*?)(<\/div>)/s',
                '$1<br>4. If NO to any, REVISE CATEGORIZATION [Yes]$2',
                $display_summary
            );
        }
    } else {
        // If no verification section exists, create one with all 4 points
        $verification_section = "<h4 style=\"color: #0066cc; font-weight: bold; margin-top: 15px;\">VERIFICATION:</h4>\n";
        $verification_section .= "<div style=\"margin-left: 20px;\">\n";
        $verification_section .= "1. Sum of ACPT Table = ? [Yes]\n<br>";
        $verification_section .= "2. Sum of Aligned Table = ? [Yes]\n<br>";
        $verification_section .= "3. Every comment accounted for? [Yes]\n<br>";
        $verification_section .= "4. If NO to any, REVISE CATEGORIZATION [Yes]\n";
        $verification_section .= "</div>";

        // Add the verification section before the Sample Comments section
        if (strpos($display_summary, 'Sample Comments') !== false) {
            $display_summary = preg_replace(
                '/(.*?)(<h3.*?>.*?Sample\s+Comments.*?<\/h3>)/s',
                '$1' . $verification_section . '$2',
                $display_summary
            );
        } else {
            // If no Sample Comments section, add at the end
            $display_summary .= $verification_section;
        }
    }

    // Format tables for display - improved to handle separate tables correctly

    // First, identify and mark the section headers for tables to ensure they're properly separated
    $display_summary = preg_replace('/(4\.\s+ACPT\s+Table:)/i', '###TABLE_SECTION_MARKER###$1', $display_summary);
    $display_summary = preg_replace('/(5\.\s+ACPT-Aligned\s+Sub\s+Driver\s+&\s+Sentiment\s+Table:)/i', '###TABLE_SECTION_MARKER###$1', $display_summary);

    // Split the content by the markers to process each table section separately
    $sections = explode('###TABLE_SECTION_MARKER###', $display_summary);
    $display_summary = $sections[0]; // Start with the first section

    // Process each table section separately
    for ($i = 1; $i < count($sections); $i++) {
        $section = $sections[$i];

        // Find the table in this section
        $pattern = '/\|(.*?)\|\s*\n\|([-|\s]+)\|\s*\n((?:\|.*?\|\s*\n)+)/s';
        if (preg_match($pattern, $section, $match)) {
            $original_table = $match[0];
            $header = $match[1];
            $rows = $match[3];

            // Determine which table we're processing
            $is_acpt_table = strpos($section, 'ACPT Table:') !== false && strpos($section, 'ACPT-Aligned') === false;
            $is_aligned_table = strpos($section, 'ACPT-Aligned Sub Driver & Sentiment Table:') !== false;

            // Count columns in header
            $header_cols = explode('|', trim($header, '|'));

            // Create HTML table with appropriate styling
            $html_table = '<table class="acpt-table" border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%; margin-bottom: 15px; margin-top: 10px;">';

            // Add header with appropriate styling
            $html_table .= '<tr style="background-color: #f2f2f2;">';

            // Use appropriate headers based on the table type
            if ($is_acpt_table) {
                $table_headers = ['ACPT Category', 'Issue Description', 'Count', 'Sample Comment'];
                foreach ($table_headers as $col) {
                    $html_table .= '<th style="border: 1px solid #ddd; padding: 8px; text-align: left; color: #0066cc;">' . trim($col) . '</th>';
                }
            } else if ($is_aligned_table) {
                $table_headers = ['ACPT Category', 'L1 Category', 'Sub Driver (L2)', 'Sentiment', 'Count', 'Sample Comment'];
                foreach ($table_headers as $col) {
                    $html_table .= '<th style="border: 1px solid #ddd; padding: 8px; text-align: left; color: #0066cc;">' . trim($col) . '</th>';
                }
            } else {
                // Use the original headers if not a recognized table
                foreach ($header_cols as $col) {
                    $html_table .= '<th style="border: 1px solid #ddd; padding: 8px; text-align: left; color: #0066cc;">' . trim($col) . '</th>';
                }
            }

            $html_table .= '</tr>';

            // Add rows and calculate totals
            $row_array = explode("\n", trim($rows));

            // Calculate total count for tables
            $total_count = 0;
            $count_column = $is_acpt_table ? 2 : 4; // Count column is 3rd for ACPT Table, 5th for ACPT-Aligned Table

            foreach ($row_array as $row) {
                if (empty(trim($row))) continue;
                if (stripos($row, 'TOTAL') !== false || stripos($row, 'Sum of Count') !== false) continue;

                $cols = explode('|', trim($row, '|'));
                if (count($cols) > $count_column) {
                    $count_value = trim($cols[$count_column]);
                    if (is_numeric($count_value)) {
                        $total_count += intval($count_value);
                    }
                }
            }

            // Process each row
            foreach ($row_array as $row) {
                if (empty(trim($row))) continue;

                // Skip if this is a total row - we'll add our own
                if (stripos($row, 'TOTAL') !== false || stripos($row, 'Sum of Count') !== false) {
                    continue;
                }

                $html_table .= '<tr>';
                $cols = explode('|', trim($row, '|'));

                // Handle different column counts for different tables
                if ($is_acpt_table && count($cols) > 4) {
                    // ACPT Table should have 4 columns
                    $processed_cols = array();
                    $processed_cols[0] = isset($cols[0]) ? trim($cols[0]) : '';
                    $processed_cols[1] = isset($cols[1]) ? trim($cols[1]) : '';
                    $processed_cols[2] = isset($cols[2]) ? trim($cols[2]) : '';

                    // Combine remaining columns for the sample comment
                    $sample_comment = '';
                    for ($j = 3; $j < count($cols); $j++) {
                        $sample_comment .= trim($cols[$j]) . ' ';
                    }
                    $processed_cols[3] = trim($sample_comment);

                    $cols = $processed_cols;
                } else if ($is_aligned_table && count($cols) > 6) {
                    // ACPT-Aligned Table should have 6 columns
                    $processed_cols = array();
                    $processed_cols[0] = isset($cols[0]) ? trim($cols[0]) : '';
                    $processed_cols[1] = isset($cols[1]) ? trim($cols[1]) : '';
                    $processed_cols[2] = isset($cols[2]) ? trim($cols[2]) : '';
                    $processed_cols[3] = isset($cols[3]) ? trim($cols[3]) : '';
                    $processed_cols[4] = isset($cols[4]) ? trim($cols[4]) : '';

                    // Combine remaining columns for the sample comment
                    $sample_comment = '';
                    for ($j = 5; $j < count($cols); $j++) {
                        $sample_comment .= trim($cols[$j]) . ' ';
                    }
                    $processed_cols[5] = trim($sample_comment);

                    $cols = $processed_cols;
                }

                foreach ($cols as $col) {
                    $col_content = trim($col);

                    // Check if this is a TOTAL row
                    $is_total = stripos($col_content, 'TOTAL') !== false;

                    // Style for TOTAL row
                    $style = $is_total ? 'font-weight: bold; background-color: #f2f2f2;' : '';

                    // Check if content is a sample comment (in quotes)
                    if (preg_match('/^"(.*)"$/', $col_content, $matches)) {
                        $html_table .= '<td style="border: 1px solid #ddd; padding: 8px; text-align: left; ' . $style . '"><span style="color: #006600; font-style: italic;">"' . $matches[1] . '"</span></td>';
                    } else {
                        $html_table .= '<td style="border: 1px solid #ddd; padding: 8px; text-align: left; ' . $style . '">' . $col_content . '</td>';
                    }
                }

                $html_table .= '</tr>';
            }

            // Add total row for ACPT Table
            if ($is_acpt_table && $total_count > 0) {
                $html_table .= '<tr style="font-weight: bold; background-color: #f2f2f2;">';
                $html_table .= '<td colspan="2" style="border: 1px solid #ddd; padding: 8px; text-align: right;">Total Count =</td>';
                $html_table .= "<td style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">{$total_count}</td>";
                $html_table .= '<td style="border: 1px solid #ddd; padding: 8px; text-align: left;"></td>';
                $html_table .= '</tr>';
            }

            // Add total row for ACPT-Aligned Table
            if ($is_aligned_table && $total_count > 0) {
                $html_table .= '<tr style="font-weight: bold; background-color: #f2f2f2;">';
                $html_table .= '<td colspan="4" style="border: 1px solid #ddd; padding: 8px; text-align: right;">Total Count =</td>';
                $html_table .= "<td style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">{$total_count}</td>";
                $html_table .= '<td style="border: 1px solid #ddd; padding: 8px; text-align: left;"></td>';
                $html_table .= '</tr>';
            }

            $html_table .= '</table>';

            // Replace original table with HTML table
            $section = str_replace($original_table, $html_table, $section);
        }

        // Add the processed section back to the display summary
        $display_summary .= $section;
    }

    // Format bullet points for display
    $display_summary = preg_replace('/^\s*[-•]\s+(.*?)$/m', '<li style="margin-left: 20px;">$1</li>', $display_summary);
    $display_summary = preg_replace('/(<li.*?<\/li>\s*)+/', '<ul style="list-style-type: disc; margin-left: 20px;">$0</ul>', $display_summary);

    // Format verification list items for display
    $display_summary = preg_replace('/^\s*(\d+)\.\s+(.*?)$/m', '<li style="margin-left: 20px;">$1. $2</li>', $display_summary);

    // Convert remaining bold text for display
    $display_summary = preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>', $display_summary);

    // Preserve line breaks for display
    $display_summary = nl2br($display_summary);

    // For database storage, we'll use the original summary with markdown
    // This is what will be saved to the database
    $_SESSION['raw_summary'] = $summary;

    return $display_summary;
}

// Function to save summary to database
function save_summary_to_database($data_id, $summary, $domain_category, $user_id) {
    global $db;

    try {
        // Format the summary for display
        $formatted_summary = format_summary($summary);

        // Clean the summary for database storage (remove HTML and markup)
        $clean_summary = clean_summary_for_database($summary);

        // Check if a summary already exists for this data_id
        $check_query = "SELECT id FROM feedback_summaries WHERE data_id = :data_id";
        $check_stmt = $db->connect()->prepare($check_query);
        $check_stmt->bindParam(':data_id', $data_id);
        $check_stmt->execute();

        // Use ternary operator to determine whether to update or insert
        $query = $check_stmt->rowCount() > 0
            ? "UPDATE feedback_summaries
               SET summary = :summary,
                   domain_category = :domain_category,
                   user_id = :user_id,
                   created_at = CURRENT_TIMESTAMP
               WHERE data_id = :data_id"
            : "INSERT INTO feedback_summaries
               (data_id, summary, domain_category, user_id)
               VALUES
               (:data_id, :summary, :domain_category, :user_id)";

        $stmt = $db->connect()->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->bindParam(':summary', $clean_summary); // Use the cleaned summary
        $stmt->bindParam(':domain_category', $domain_category);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        // Store both the formatted and clean summary in session for display purposes
        $_SESSION['formatted_summary'] = $formatted_summary;
        $_SESSION['clean_summary'] = $clean_summary;

        // Validate the table counts
        validate_table_counts($summary);

        return true;
    } catch (PDOException $e) {
        error_log("Error saving summary to database: " . $e->getMessage());
        return false;
    }
}
// Function to clean summary for database storage
function clean_summary_for_database($summary) {
    // Process the original summary

    // Define the exact order and numbering of sections
    $sections = [
        '1' => 'Summary',
        '2' => 'Top Pain Points or Praises',
        '3' => 'Key Takeaways',
        '4' => 'ACPT Table',
        '5' => 'ACPT-Aligned Sub Driver & Sentiment Table'
    ];

    // Create a formatted summary in the desired format
    $formatted_summary = "";

    // Extract all section content first
    $extracted_sections = [];

    // Extract verification section
    $verification_content = "";
    if (preg_match('/\*\*VERIFICATION:\*\*(.*?)(?=\n\s*\d+\.\s*\*\*|\z)/s', $summary, $verification_matches)) {
        $verification_content = trim($verification_matches[1]);
    }

    // Extract all sections from the summary
    foreach ($sections as $num => $title) {
        // Try multiple patterns to find each section
        $patterns = [
            // Exact numbered match (e.g., "2. **Top Pain Points or Praises:**")
            '/\b' . $num . '\.\s*\*\*' . preg_quote($title, '/') . '.*?\*\*:?(.*?)(?=(?:\d+\.)?\s*\*\*(?:' . implode('|', array_map('preg_quote', $sections)) . ').*?\*\*:|\z)/s',

            // Any numbered match (e.g., "1. **Top Pain Points or Praises:**")
            '/\b\d+\.\s*\*\*' . preg_quote($title, '/') . '.*?\*\*:?(.*?)(?=(?:\d+\.)?\s*\*\*(?:' . implode('|', array_map('preg_quote', $sections)) . ').*?\*\*:|\z)/s',

            // Unnumbered match (e.g., "**Top Pain Points or Praises:**")
            '/\*\*' . preg_quote($title, '/') . '.*?\*\*:?(.*?)(?=(?:\d+\.)?\s*\*\*(?:' . implode('|', array_map('preg_quote', $sections)) . ').*?\*\*:|\z)/s',

            // Simple text match (e.g., "Top Pain Points or Praises:")
            '/' . preg_quote($title, '/') . ':?(.*?)(?=(?:\d+\.)?\s*(?:' . implode('|', array_map('preg_quote', $sections)) . '):|\z)/s'
        ];

        // Try each pattern until we find a match
        $section_content = "";
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $summary, $matches)) {
                $section_content = trim($matches[1]);
                break;
            }
        }

        // Store the extracted content
        $extracted_sections[$num] = $section_content;
    }

    // Now build the formatted summary in the exact order specified
    foreach ($sections as $num => $title) {
        $section_content = isset($extracted_sections[$num]) ? $extracted_sections[$num] : "";

        // Skip if this section is empty
        if (empty($section_content)) {
            continue;
        }

        // Always use the exact section number and title
        $formatted_summary .= "{$num}. {$title}:  \n";

        // Process the content based on section type
        if ($num == '2') { // Top Pain Points or Praises - bullet points
            // Extract bullet points
            preg_match_all('/^\s*[-•*]\s+(.*?)$/m', $section_content, $bullet_matches);
            if (!empty($bullet_matches[1])) {
                foreach ($bullet_matches[1] as $bullet) {
                    $formatted_summary .= "- " . trim($bullet) . "  \n";
                }
            }
        } else if ($num == '4' || $num == '5') { // Tables
            // Extract table - look for the first table in this section
            $table_pattern = '/\|(.*?)\|\s*\n\|([-|\s]+)\|\s*\n((?:\|.*?\|\s*\n)+)/s';
            if (preg_match($table_pattern, $section_content, $table_matches)) {
                // Add table header
                $formatted_summary .= "| " . trim($table_matches[1]) . " |  \n";
                $formatted_summary .= "| " . trim($table_matches[2]) . " |  \n";

                // Add table rows
                $rows = trim($table_matches[3]);
                $row_array = explode("\n", $rows);

                // Calculate total for tables
                $total = 0;
                $count_column = ($num == '4') ? 2 : 4; // Count column is 3rd for ACPT Table, 5th for ACPT-Aligned Table

                foreach ($row_array as $row) {
                    if (empty(trim($row))) continue;
                    if (stripos($row, 'TOTAL') !== false || stripos($row, 'Sum of Count') !== false) continue;

                    $cols = explode('|', trim($row, '|'));
                    if (count($cols) > $count_column) {
                        $count_value = trim($cols[$count_column]);
                        if (is_numeric($count_value)) {
                            $total += intval($count_value);
                        }
                    }

                    // Add the row to the formatted summary
                    if (!empty(trim($row))) {
                        $formatted_summary .= trim($row) . "  \n";
                    }
                }

                // Add sum of count for tables
                $formatted_summary .= "\nSum of Count = {$total}  \n";
            }
        } else {
            // Regular text section - just add the content with proper formatting
            // Remove any tables that might be in this section
            $section_content = preg_replace('/\|(.*?)\|\s*\n\|([-|\s]+)\|\s*\n((?:\|.*?\|\s*\n)+)/s', '', $section_content);

            // Remove any TOTAL rows that might be in this section
            $section_content = preg_replace('/\|\s*TOTAL\s*\|.*?\|\s*\d+\s*\|.*?\|/s', '', $section_content);

            // Remove any table headers that might be in this section
            $section_content = preg_replace('/ACPT Table:.*?(?=\n\d+\.\s*|\z)/s', '', $section_content);
            $section_content = preg_replace('/ACPT-Aligned Sub Driver & Sentiment Table:.*?(?=\n\d+\.\s*|\z)/s', '', $section_content);

            // Remove any verification sections that might be in this section
            $section_content = preg_replace('/VERIFICATION:.*?(?=\d+\.\s*|\z)/s', '', $section_content);
            $section_content = preg_replace('/\d+\.\s+Sum of ACPT Table.*?\[Yes\]/s', '', $section_content);
            $section_content = preg_replace('/\d+\.\s+Sum of Aligned Table.*?\[Yes\]/s', '', $section_content);
            $section_content = preg_replace('/\d+\.\s+Every comment accounted for\?.*?\[Yes\]/s', '', $section_content);

            // Remove bold formatting
            $section_content = preg_replace('/\*\*(.*?)\*\*/', '$1', $section_content);

            // Normalize line breaks
            $section_content = preg_replace('/\n+/', "\n", $section_content);

            // Add the cleaned content
            if (!empty(trim($section_content))) {
                $formatted_summary .= "{$section_content}  \n\n";
            }
        }

        // Add extra line break between sections
        $formatted_summary .= "\n";
    }

    // Add verification section at the end if it exists
    if (!empty($verification_content)) {
        $formatted_summary .= "VERIFICATION:  \n";

        // Extract verification points
        preg_match_all('/^\s*(\d+)\.\s+(.*?)\?\s+\[(Yes|No)\]/m', $verification_content, $verification_matches);
        if (!empty($verification_matches[0])) {
            for ($i = 0; $i < count($verification_matches[0]); $i++) {
                $point_num = $verification_matches[1][$i];
                $point_text = $verification_matches[2][$i];
                $point_answer = $verification_matches[3][$i];
                $formatted_summary .= "{$point_num}. {$point_text}? [{$point_answer}]  \n";
            }
        }

        // Add the 4th point if missing
        if (!preg_match('/4\.\s+If NO to any/', $verification_content)) {
            $formatted_summary .= "4. If NO to any, REVISE CATEGORIZATION [Yes]  \n";
        }
    }

    // Clean up any remaining markdown or HTML
    $formatted_summary = strip_tags($formatted_summary);

    // Remove any markdown formatting that might remain
    $formatted_summary = preg_replace('/\*\*(.*?)\*\*/', '$1', $formatted_summary); // Bold
    $formatted_summary = preg_replace('/\*(.*?)\*/', '$1', $formatted_summary); // Italic
    $formatted_summary = preg_replace('/__(.*?)__/', '$1', $formatted_summary); // Underline

    // Remove any duplicate verification sections
    $formatted_summary = preg_replace('/(VERIFICATION:.*?)VERIFICATION:/s', '$1', $formatted_summary);

    // Remove any duplicate table sections
    $formatted_summary = preg_replace('/(\d+\. ACPT Table:.*?)(\d+\. ACPT Table:)/s', '$1', $formatted_summary);
    $formatted_summary = preg_replace('/(\d+\. ACPT-Aligned Sub Driver & Sentiment Table:.*?)(\d+\. ACPT-Aligned Sub Driver & Sentiment Table:)/s', '$1', $formatted_summary);

    // Remove any duplicate section headers (unnumbered versions)
    $formatted_summary = preg_replace('/Top Pain Points or Praises:\s*\n((?:- .*?\n)+)(\n*2\. Top Pain Points or Praises:)/s', '$2', $formatted_summary);
    $formatted_summary = preg_replace('/Key Takeaways:\s*\n(.*?)(\n*3\. Key Takeaways:)/s', '$2', $formatted_summary);

    // Remove table headers without actual tables
    $formatted_summary = preg_replace('/ACPT Table:\s*\n(?!\|)/s', '', $formatted_summary);
    $formatted_summary = preg_replace('/ACPT-Aligned Sub Driver & Sentiment Table:\s*\n(?!\|)/s', '', $formatted_summary);

    // Remove any standalone TOTAL rows
    $formatted_summary = preg_replace('/\|\s*TOTAL\s*\|.*?\|\s*\d+\s*\|.*?\|\s*\n/s', '', $formatted_summary);

    // Ensure proper spacing
    $formatted_summary = preg_replace('/\n{3,}/', "\n\n", $formatted_summary);

    // Make sure there are no double spaces at the end of lines
    $formatted_summary = preg_replace('/  \n/', "\n", $formatted_summary);

    // Final cleanup - ensure we have only the correctly numbered sections

    return trim($formatted_summary);
}

// Place helper functions at the top for availability
function count_comments_in_tables($summary) {
    $counts = [
        'acpt' => 0,
        'aligned' => 0
    ];

    // Extract ACPT Table section
    if (preg_match('/4\.\s+ACPT Table:(.*?)(?=5\.\s+ACPT-Aligned|VERIFICATION:|$)/s', $summary, $acpt_section)) {
        $acpt_content = $acpt_section[1];

        // Find Sum of Count in ACPT Table
        if (preg_match('/Sum of Count\s*=\s*(\d+)/', $acpt_content, $acpt_sum)) {
            $counts['acpt'] = intval($acpt_sum[1]);
        } else {
            // If no Sum of Count, calculate from rows
            preg_match_all('/\|\s*(?:Agent|Process|Technology|Customer|Uncategorized)\s*\|\s*.*?\|\s*(\d+)\s*\|/', $acpt_content, $acpt_matches);
            if (!empty($acpt_matches[1])) {
                $counts['acpt'] = array_sum(array_map('intval', $acpt_matches[1]));
            }
        }
    }

    // Extract ACPT-Aligned Table section
    if (preg_match('/5\.\s+ACPT-Aligned Sub Driver & Sentiment Table:(.*?)(?=VERIFICATION:|$)/s', $summary, $aligned_section)) {
        $aligned_content = $aligned_section[1];

        // Find Sum of Count in ACPT-Aligned Table
        if (preg_match('/Sum of Count\s*=\s*(\d+)/', $aligned_content, $aligned_sum)) {
            $counts['aligned'] = intval($aligned_sum[1]);
        } else {
            // If no Sum of Count, calculate from rows
            preg_match_all('/\|\s*(?:Agent|Process|Technology|Customer|Uncategorized)\s*\|\s*.*?\|\s*.*?\|\s*.*?\|\s*(\d+)\s*\|/', $aligned_content, $aligned_matches);
            if (!empty($aligned_matches[1])) {
                $counts['aligned'] = array_sum(array_map('intval', $aligned_matches[1]));
            }
        }
    }

    return $counts;
}

function validate_table_counts($summary) {
    $total_comments = isset($_POST['total_comments']) ? intval($_POST['total_comments']) : 0;

    // Skip validation if no total_comments provided
    if ($total_comments <= 0) {
        return;
    }

    $counts = count_comments_in_tables($summary);

    // Log validation details
    error_log("Validating table counts for total_count: {$total_comments}");
    error_log("ACPT Table count found: {$counts['acpt']}");
    error_log("ACPT-Aligned Table count found: {$counts['aligned']}");

    // Only log mismatches if they're significant
    if (abs($counts['acpt'] - $total_comments) > 1) {
        error_log("ACPT Table count mismatch: {$counts['acpt']} vs {$total_comments}");
    }

    if (abs($counts['aligned'] - $total_comments) > 1) {
        error_log("ACPT-Aligned Table count mismatch: {$counts['aligned']} vs {$total_comments}");
    }
}


?>